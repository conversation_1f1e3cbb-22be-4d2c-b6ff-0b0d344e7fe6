'use client'

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  Trophy, 
  Target, 
  TrendingUp, 
  Clock, 
  Star, 
  Shield, 
  Eye, 
  EyeOff,
  Calendar,
  Award,
  Flame,
  BarChart3
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { createClient } from '../../supabase/client';
import StyledAvatar from './styled-avatar';
import LevelDisplay from './level-display';
import { getPlayerStats, getUserMatchHistory } from '@/utils/profile-utils';
import { getLevelInfo, formatXP } from '@/utils/level-system';

interface PlayerStatsPopupProps {
  children: React.ReactNode;
  playerId: string;
  playerName: string;
  playerAvatar?: string;
  playerBorder?: string;
}

interface PlayerProfile {
  id: string;
  display_name: string;
  avatar_url?: string;
  avatar_border?: string;
  title?: string;
  background_display?: string;
  level_experience: number;
  created_at: string;
  privacy_settings?: {
    show_games_played?: boolean;
    show_wins?: boolean;
    show_win_rate?: boolean;
    show_highest_score?: boolean;
    show_longest_streak?: boolean;
    show_survival_rate?: boolean;
    show_match_history?: boolean;
    show_level?: boolean;
  };
}

export default function PlayerStatsPopup({ 
  children, 
  playerId, 
  playerName, 
  playerAvatar,
  playerBorder 
}: PlayerStatsPopupProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [playerProfile, setPlayerProfile] = useState<PlayerProfile | null>(null);
  const [playerStats, setPlayerStats] = useState<any>(null);
  const [recentMatches, setRecentMatches] = useState<any[]>([]);
  const [isOwnProfile, setIsOwnProfile] = useState(false);

  useEffect(() => {
    if (open) {
      loadPlayerData();
    }
  }, [open, playerId]);

  const loadPlayerData = async () => {
    setLoading(true);
    try {
      const supabase = createClient();
      
      // Check if viewing own profile
      const { data: { user } } = await supabase.auth.getUser();
      setIsOwnProfile(user?.id === playerId);

      // Get player profile data
      const { data: profile, error: profileError } = await supabase
        .from('players')
        .select('*')
        .eq('id', playerId)
        .single();

      if (profileError) {
        console.error('Error fetching player profile:', profileError);
        return;
      }

      setPlayerProfile(profile);

      // Get player stats and match history
      const [stats, matches] = await Promise.all([
        getPlayerStats(playerId),
        getUserMatchHistory(playerId, 5)
      ]);

      setPlayerStats(stats);
      setRecentMatches(matches);

    } catch (error) {
      console.error('Error loading player data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getPrivacySetting = (setting: keyof NonNullable<PlayerProfile['privacy_settings']>) => {
    if (isOwnProfile) return true; // Always show own stats
    return playerProfile?.privacy_settings?.[setting] !== false; // Default to true if not set
  };

  const levelInfo = playerProfile ? getLevelInfo(playerProfile.level_experience || 0) : null;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="sr-only">Player Profile</DialogTitle>
          {/* Custom Header with Background */}
          <div className={cn(
            "relative -mx-6 -mt-6 px-6 pt-6 pb-4 mb-4 rounded-t-lg overflow-hidden",
            playerProfile?.background_display || "bg-gradient-to-r from-amber-100 to-amber-50"
          )}>
            <div className="flex items-center gap-4">
              <StyledAvatar
                src={playerAvatar}
                alt={playerName}
                fallback={playerName.substring(0, 2).toUpperCase()}
                size="xl"
                border={playerBorder}
              />
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-amber-900">{playerName}</h2>
                {playerProfile?.title && (
                  <p className="text-sm text-amber-700 font-medium">{playerProfile.title}</p>
                )}
                {levelInfo && getPrivacySetting('show_level') && (
                  <div className="mt-2">
                    <LevelDisplay
                      totalXP={playerProfile?.level_experience || 0}
                      variant="compact"
                      showProgress={false}
                      showTitle={true}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="py-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
            <p className="text-lg font-medium text-amber-900">Loading player stats...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Level Progress Details */}
            {levelInfo && getPrivacySetting('show_level') && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Star className="h-5 w-5 text-amber-600" />
                    Level Progress
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <LevelDisplay
                    totalXP={playerProfile?.level_experience || 0}
                    variant="detailed"
                    showProgress={true}
                    showTitle={false}
                  />
                </CardContent>
              </Card>
            )}

            {/* Stats Overview */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-amber-600" />
                  Statistics
                  {!isOwnProfile && (
                    <Badge variant="outline" className="text-xs">
                      <Eye className="h-3 w-3 mr-1" />
                      Public View
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {getPrivacySetting('show_games_played') && (
                    <div className="text-center p-3 bg-amber-50 rounded-lg">
                      <p className="text-2xl font-bold text-amber-700">{playerStats?.total_matches || 0}</p>
                      <p className="text-sm text-gray-600">Games Played</p>
                    </div>
                  )}
                  
                  {getPrivacySetting('show_wins') && (
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <p className="text-2xl font-bold text-green-700">{playerStats?.wins || 0}</p>
                      <p className="text-sm text-gray-600">Wins</p>
                    </div>
                  )}
                  
                  {getPrivacySetting('show_win_rate') && (
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <p className="text-2xl font-bold text-blue-700">{playerStats?.win_rate || 0}%</p>
                      <p className="text-sm text-gray-600">Win Rate</p>
                    </div>
                  )}
                  
                  {getPrivacySetting('show_highest_score') && (
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <p className="text-2xl font-bold text-purple-700">{playerStats?.highest_score?.toLocaleString() || 0}</p>
                      <p className="text-sm text-gray-600">Highest Score</p>
                    </div>
                  )}
                  
                  {getPrivacySetting('show_longest_streak') && (
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <p className="text-2xl font-bold text-orange-700">{playerStats?.longest_streak || 0}</p>
                      <p className="text-sm text-gray-600">Longest Streak</p>
                    </div>
                  )}
                  
                  {getPrivacySetting('show_survival_rate') && (
                    <div className="text-center p-3 bg-red-50 rounded-lg">
                      <p className="text-2xl font-bold text-red-700">{playerStats?.survival_rate || 0}%</p>
                      <p className="text-sm text-gray-600">Survival Rate</p>
                    </div>
                  )}
                </div>

                {/* Privacy Notice */}
                {!isOwnProfile && (
                  <div className="mt-4 p-3 bg-gray-50 rounded-lg border">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Shield className="h-4 w-4" />
                      <span>Some stats may be hidden based on player's privacy settings</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Matches */}
            {getPrivacySetting('show_match_history') && recentMatches.length > 0 && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Clock className="h-5 w-5 text-amber-600" />
                    Recent Matches
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentMatches.map((match, index) => (
                      <div key={match.match_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            {match.final_rank === 1 && <Trophy className="h-4 w-4 text-yellow-500" />}
                            {match.final_rank === 2 && <Award className="h-4 w-4 text-gray-400" />}
                            {match.final_rank === 3 && <Award className="h-4 w-4 text-amber-600" />}
                            {match.final_rank > 3 && <Target className="h-4 w-4 text-gray-500" />}
                            <span className="font-medium text-sm">
                              #{match.final_rank}
                            </span>
                          </div>
                          
                          <Badge className={cn(
                            "text-xs capitalize",
                            match.difficulty === 'easy' && "bg-green-100 text-green-700",
                            match.difficulty === 'medium' && "bg-blue-100 text-blue-700",
                            match.difficulty === 'hard' && "bg-orange-100 text-orange-700",
                            match.difficulty === 'extreme' && "bg-purple-100 text-purple-700"
                          )}>
                            {match.difficulty}
                          </Badge>
                          
                          <span className="text-sm text-gray-600">
                            {match.total_players} players
                          </span>
                        </div>
                        
                        <div className="text-right">
                          <p className="font-medium text-sm">{match.score.toLocaleString()} pts</p>
                          <p className="text-xs text-gray-500">{formatDate(match.ended_at)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
