-- Weekly Reset System Schema
-- This file contains the database schema for implementing weekly leaderboard resets

-- Create system_settings table to track weekly reset information
CREATE TABLE IF NOT EXISTS system_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  setting_key TEXT UNIQUE NOT NULL,
  setting_value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert initial weekly reset settings
INSERT INTO system_settings (setting_key, setting_value) 
VALUES (
  'weekly_reset',
  jsonb_build_object(
    'last_reset_date', NOW()::date,
    'reset_day_of_week', 1, -- Monday (0=Sunday, 1=Monday, etc.)
    'reset_hour', 0, -- Midnight UTC
    'reset_minute', 0,
    'is_enabled', true,
    'current_week_number', EXTRACT(week FROM NOW()),
    'current_year', EXTRACT(year FROM NOW())
  )
) ON CONFLICT (setting_key) DO NOTHING;

-- Add weekly reset tracking columns to user_stats if they don't exist
DO $$ 
BEGIN
  -- Add last_weekly_reset column to track when user's weekly score was last reset
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_stats' AND column_name = 'last_weekly_reset'
  ) THEN
    ALTER TABLE user_stats ADD COLUMN last_weekly_reset TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  END IF;

  -- Add weekly_rank column to store current weekly ranking
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_stats' AND column_name = 'weekly_rank'
  ) THEN
    ALTER TABLE user_stats ADD COLUMN weekly_rank INTEGER DEFAULT NULL;
  END IF;

  -- Add previous_weekly_score to track last week's performance
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'user_stats' AND column_name = 'previous_weekly_score'
  ) THEN
    ALTER TABLE user_stats ADD COLUMN previous_weekly_score INTEGER DEFAULT 0;
  END IF;
END $$;

-- Create function to get next weekly reset timestamp
CREATE OR REPLACE FUNCTION get_next_weekly_reset()
RETURNS TIMESTAMP WITH TIME ZONE AS $$
DECLARE
  reset_settings JSONB;
  reset_day INTEGER;
  reset_hour INTEGER;
  reset_minute INTEGER;
  current_time TIMESTAMP WITH TIME ZONE;
  next_reset TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get reset settings
  SELECT setting_value INTO reset_settings
  FROM system_settings
  WHERE setting_key = 'weekly_reset';

  IF reset_settings IS NULL THEN
    -- Default to Monday midnight UTC if no settings found
    reset_day := 1;
    reset_hour := 0;
    reset_minute := 0;
  ELSE
    reset_day := (reset_settings->>'reset_day_of_week')::INTEGER;
    reset_hour := (reset_settings->>'reset_hour')::INTEGER;
    reset_minute := (reset_settings->>'reset_minute')::INTEGER;
  END IF;

  current_time := NOW() AT TIME ZONE 'UTC';
  
  -- Calculate next reset time
  next_reset := date_trunc('week', current_time) + 
                INTERVAL '1 day' * reset_day + 
                INTERVAL '1 hour' * reset_hour + 
                INTERVAL '1 minute' * reset_minute;
  
  -- If the calculated time is in the past, add a week
  IF next_reset <= current_time THEN
    next_reset := next_reset + INTERVAL '1 week';
  END IF;

  RETURN next_reset;
END;
$$ LANGUAGE plpgsql;

-- Create function to check if weekly reset is due
CREATE OR REPLACE FUNCTION is_weekly_reset_due()
RETURNS BOOLEAN AS $$
DECLARE
  reset_settings JSONB;
  last_reset_date DATE;
  current_date DATE;
  reset_day INTEGER;
  days_since_reset INTEGER;
BEGIN
  -- Get reset settings
  SELECT setting_value INTO reset_settings
  FROM system_settings
  WHERE setting_key = 'weekly_reset';

  IF reset_settings IS NULL OR (reset_settings->>'is_enabled')::BOOLEAN = FALSE THEN
    RETURN FALSE;
  END IF;

  last_reset_date := (reset_settings->>'last_reset_date')::DATE;
  reset_day := (reset_settings->>'reset_day_of_week')::INTEGER;
  current_date := NOW()::DATE;
  
  -- Calculate days since last reset
  days_since_reset := current_date - last_reset_date;
  
  -- Check if it's been a week or more since last reset
  -- and if today is the reset day or past it
  IF days_since_reset >= 7 THEN
    -- Check if current day of week is >= reset day
    IF EXTRACT(dow FROM current_date) >= reset_day OR 
       (EXTRACT(dow FROM current_date) = 0 AND reset_day = 0) THEN
      RETURN TRUE;
    END IF;
  END IF;

  RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Create function to perform weekly reset
CREATE OR REPLACE FUNCTION perform_weekly_reset()
RETURNS JSONB AS $$
DECLARE
  reset_count INTEGER := 0;
  reset_settings JSONB;
  current_week INTEGER;
  current_year INTEGER;
BEGIN
  -- Check if reset is due
  IF NOT is_weekly_reset_due() THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Weekly reset is not due yet',
      'reset_count', 0
    );
  END IF;

  -- Store previous weekly scores before reset
  UPDATE user_stats 
  SET previous_weekly_score = weekly_scores,
      last_weekly_reset = NOW()
  WHERE weekly_scores > 0;

  -- Reset all weekly scores
  UPDATE user_stats 
  SET weekly_scores = 0,
      weekly_rank = NULL,
      last_weekly_reset = NOW();

  GET DIAGNOSTICS reset_count = ROW_COUNT;

  -- Update system settings with new reset date
  current_week := EXTRACT(week FROM NOW());
  current_year := EXTRACT(year FROM NOW());

  UPDATE system_settings 
  SET setting_value = jsonb_set(
        jsonb_set(
          jsonb_set(setting_value, '{last_reset_date}', to_jsonb(NOW()::date)),
          '{current_week_number}', to_jsonb(current_week)
        ),
        '{current_year}', to_jsonb(current_year)
      ),
      updated_at = NOW()
  WHERE setting_key = 'weekly_reset';

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Weekly reset completed successfully',
    'reset_count', reset_count,
    'reset_date', NOW()
  );
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_stats_weekly_scores ON user_stats(weekly_scores DESC, difficulty);
CREATE INDEX IF NOT EXISTS idx_user_stats_weekly_reset ON user_stats(last_weekly_reset);
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key);

-- Create RLS policies for system_settings (if RLS is enabled)
-- Note: Adjust these policies based on your security requirements
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE tablename = 'system_settings' 
    AND rowsecurity = true
  ) THEN
    -- Allow read access to system settings for authenticated users
    CREATE POLICY IF NOT EXISTS "Allow read access to system settings" 
    ON system_settings FOR SELECT 
    TO authenticated 
    USING (true);

    -- Restrict write access to system settings (admin only)
    -- You may want to create a custom role for admin users
    CREATE POLICY IF NOT EXISTS "Restrict write access to system settings" 
    ON system_settings FOR ALL 
    TO authenticated 
    USING (false);
  END IF;
END $$;
