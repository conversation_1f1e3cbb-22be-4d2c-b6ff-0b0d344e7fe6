import React from 'react';
import { createClient } from '../../../supabase/server';
import { redirect } from 'next/navigation';
import DashboardNavbar from "@/components/dashboard-navbar";
import FeedbackForm from "@/components/feedback-form";
import FeedbackList from "@/components/feedback-list";
import {
  MessageSquare,
  Bug,
  Lightbulb,
  Star,
  TrendingUp,
  Users,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default async function FeedbackPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/sign-in");
  }

  // Get user's feedback
  const { data: userFeedback } = await supabase
    .from('feedback')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  // Get feedback stats
  const { data: feedbackStats } = await supabase
    .from('feedback')
    .select('category, status')
    .eq('user_id', user.id);

  const stats = {
    total: feedbackStats?.length || 0,
    pending: feedbackStats?.filter(f => f.status === 'pending').length || 0,
    resolved: feedbackStats?.filter(f => f.status === 'resolved').length || 0,
    bugs: feedbackStats?.filter(f => f.category === 'bug_report').length || 0,
    features: feedbackStats?.filter(f => f.category === 'feature_request').length || 0,
  };

  return (
    <>
      <DashboardNavbar />
      <main className="w-full bg-gradient-to-br from-amber-50/50 via-white to-amber-50/30 min-h-screen">
        <div className="container mx-auto px-4 py-8 flex flex-col gap-8">
          {/* Header Section */}
          <header className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <MessageSquare className="h-8 w-8 text-amber-500" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-amber-600 to-amber-800 bg-clip-text text-transparent">
                Feedback Center
              </h1>
              <MessageSquare className="h-8 w-8 text-amber-500" />
            </div>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Help us improve Word Nook! Share your thoughts, report bugs, or suggest new features.
            </p>
          </header>

          {/* Stats Cards */}
          <section className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <MessageSquare className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-blue-700">{stats.total}</p>
                  <p className="text-sm text-gray-600">Total Feedback</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-orange-700">{stats.pending}</p>
                  <p className="text-sm text-gray-600">Pending</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-green-700">{stats.resolved}</p>
                  <p className="text-sm text-gray-600">Resolved</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <Bug className="h-8 w-8 text-red-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-red-700">{stats.bugs}</p>
                  <p className="text-sm text-gray-600">Bug Reports</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <Lightbulb className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                  <p className="text-2xl font-bold text-purple-700">{stats.features}</p>
                  <p className="text-sm text-gray-600">Feature Ideas</p>
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Main Content */}
          <section>
            <Tabs defaultValue="submit" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="submit">Submit Feedback</TabsTrigger>
                <TabsTrigger value="history">My Feedback</TabsTrigger>
              </TabsList>
              
              <TabsContent value="submit" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Star className="h-5 w-5 text-amber-600" />
                      Share Your Feedback
                    </CardTitle>
                    <CardDescription>
                      Your feedback helps us make Word Nook better for everyone. Thank you for taking the time to share your thoughts!
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <FeedbackForm userId={user.id} />
                  </CardContent>
                </Card>
                
                {/* Feedback Guidelines */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Feedback Guidelines</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-semibold text-amber-900 mb-2 flex items-center gap-2">
                          <Bug className="h-4 w-4" />
                          Bug Reports
                        </h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• Describe what you expected to happen</li>
                          <li>• Explain what actually happened</li>
                          <li>• Include steps to reproduce the issue</li>
                          <li>• Mention your browser and device</li>
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold text-amber-900 mb-2 flex items-center gap-2">
                          <Lightbulb className="h-4 w-4" />
                          Feature Requests
                        </h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• Explain the problem you're trying to solve</li>
                          <li>• Describe your proposed solution</li>
                          <li>• Explain how it would benefit other players</li>
                          <li>• Consider alternative approaches</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="history" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-amber-600" />
                      Your Feedback History
                    </CardTitle>
                    <CardDescription>
                      Track the status of your submitted feedback and see our responses.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <FeedbackList feedback={userFeedback || []} />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </section>
        </div>
      </main>
    </>
  );
}
