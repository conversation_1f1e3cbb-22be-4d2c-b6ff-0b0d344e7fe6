'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useWeeklyReset } from '@/hooks/useWeeklyReset';
import { WeeklyResetInfo, WeeklyResetResult } from '@/utils/weekly-leaderboard-utils';

interface WeeklyResetContextType {
  resetInfo: WeeklyResetInfo | null;
  isLoading: boolean;
  isResetInProgress: boolean;
  lastResetResult: WeeklyResetResult | null;
  checkReset: () => Promise<void>;
  forceReset: () => Promise<WeeklyResetResult>;
  refreshResetInfo: () => Promise<void>;
}

const WeeklyResetContext = createContext<WeeklyResetContextType | undefined>(undefined);

interface WeeklyResetProviderProps {
  children: ReactNode;
}

/**
 * Provider component for weekly reset functionality
 * Should be placed at the app level to enable automatic reset checking
 */
export function WeeklyResetProvider({ children }: WeeklyResetProviderProps) {
  const weeklyResetData = useWeeklyReset();

  return (
    <WeeklyResetContext.Provider value={weeklyResetData}>
      {children}
    </WeeklyResetContext.Provider>
  );
}

/**
 * Hook to access weekly reset context
 */
export function useWeeklyResetContext(): WeeklyResetContextType {
  const context = useContext(WeeklyResetContext);
  if (context === undefined) {
    throw new Error('useWeeklyResetContext must be used within a WeeklyResetProvider');
  }
  return context;
}

/**
 * Optional notification component that shows when a reset occurs
 */
interface WeeklyResetNotificationProps {
  className?: string;
}

export function WeeklyResetNotification({ className }: WeeklyResetNotificationProps) {
  const { lastResetResult, isResetInProgress } = useWeeklyResetContext();

  if (isResetInProgress) {
    return (
      <div className={`bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded ${className || ''}`}>
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700 mr-2"></div>
          <span>Performing weekly leaderboard reset...</span>
        </div>
      </div>
    );
  }

  if (lastResetResult && lastResetResult.success && lastResetResult.reset_count > 0) {
    return (
      <div className={`bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded ${className || ''}`}>
        <div className="flex items-center">
          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <span>
            Weekly leaderboard has been reset! {lastResetResult.reset_count} player scores were reset.
          </span>
        </div>
      </div>
    );
  }

  if (lastResetResult && !lastResetResult.success) {
    return (
      <div className={`bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded ${className || ''}`}>
        <div className="flex items-center">
          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span>Failed to reset weekly leaderboard: {lastResetResult.message}</span>
        </div>
      </div>
    );
  }

  return null;
}
