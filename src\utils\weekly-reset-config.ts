import { createClient } from "../../supabase/client";

export interface WeeklyResetConfig {
  reset_day_of_week: number; // 0=Sunday, 1=Monday, etc.
  reset_hour: number; // 0-23 (UTC)
  reset_minute: number; // 0-59
  is_enabled: boolean;
  last_reset_date: string;
  current_week_number: number;
  current_year: number;
}

export interface ResetConfigUpdateResult {
  success: boolean;
  message: string;
  config?: WeeklyResetConfig;
}

/**
 * Get current weekly reset configuration
 */
export async function getWeeklyResetConfig(): Promise<WeeklyResetConfig | null> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('system_settings')
      .select('setting_value')
      .eq('setting_key', 'weekly_reset')
      .single();

    if (error) {
      console.error('Error fetching weekly reset config:', error);
      return null;
    }

    return data?.setting_value as WeeklyResetConfig;
  } catch (error) {
    console.error('Exception in getWeeklyResetConfig:', error);
    return null;
  }
}

/**
 * Update weekly reset configuration
 */
export async function updateWeeklyResetConfig(
  config: Partial<WeeklyResetConfig>
): Promise<ResetConfigUpdateResult> {
  const supabase = createClient();

  try {
    // Get current config first
    const currentConfig = await getWeeklyResetConfig();
    if (!currentConfig) {
      return {
        success: false,
        message: 'Failed to fetch current configuration'
      };
    }

    // Merge with new config
    const updatedConfig = { ...currentConfig, ...config };

    // Validate configuration
    const validation = validateResetConfig(updatedConfig);
    if (!validation.isValid) {
      return {
        success: false,
        message: validation.message
      };
    }

    // Update in database
    const { error } = await supabase
      .from('system_settings')
      .update({
        setting_value: updatedConfig,
        updated_at: new Date().toISOString()
      })
      .eq('setting_key', 'weekly_reset');

    if (error) {
      console.error('Error updating weekly reset config:', error);
      return {
        success: false,
        message: `Database error: ${error.message}`
      };
    }

    return {
      success: true,
      message: 'Weekly reset configuration updated successfully',
      config: updatedConfig
    };

  } catch (error) {
    console.error('Exception in updateWeeklyResetConfig:', error);
    return {
      success: false,
      message: `Exception: ${error}`
    };
  }
}

/**
 * Validate reset configuration
 */
function validateResetConfig(config: WeeklyResetConfig): { isValid: boolean; message: string } {
  if (config.reset_day_of_week < 0 || config.reset_day_of_week > 6) {
    return {
      isValid: false,
      message: 'Reset day must be between 0 (Sunday) and 6 (Saturday)'
    };
  }

  if (config.reset_hour < 0 || config.reset_hour > 23) {
    return {
      isValid: false,
      message: 'Reset hour must be between 0 and 23'
    };
  }

  if (config.reset_minute < 0 || config.reset_minute > 59) {
    return {
      isValid: false,
      message: 'Reset minute must be between 0 and 59'
    };
  }

  return { isValid: true, message: 'Configuration is valid' };
}

/**
 * Get human-readable day name
 */
export function getDayName(dayOfWeek: number): string {
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  return days[dayOfWeek] || 'Unknown';
}

/**
 * Format time for display
 */
export function formatResetTime(hour: number, minute: number): string {
  const date = new Date();
  date.setHours(hour, minute, 0, 0);
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short'
  });
}

/**
 * Manual reset function for testing/admin purposes
 */
export async function performManualReset(force: boolean = false): Promise<{
  success: boolean;
  message: string;
  reset_count: number;
  reset_date?: string;
}> {
  const supabase = createClient();

  try {
    if (force) {
      // Force reset by temporarily updating the last reset date
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 8); // 8 days ago to ensure reset is due

      const { error: configError } = await supabase
        .from('system_settings')
        .update({
          setting_value: {
            ...await getWeeklyResetConfig(),
            last_reset_date: oneWeekAgo.toISOString().split('T')[0]
          }
        })
        .eq('setting_key', 'weekly_reset');

      if (configError) {
        return {
          success: false,
          message: `Failed to prepare forced reset: ${configError.message}`,
          reset_count: 0
        };
      }
    }

    // Call the database function to perform reset
    const { data, error } = await supabase.rpc('perform_weekly_reset');

    if (error) {
      console.error('Error performing manual reset:', error);
      return {
        success: false,
        message: `Failed to perform reset: ${error.message}`,
        reset_count: 0
      };
    }

    return data;

  } catch (error) {
    console.error('Exception in performManualReset:', error);
    return {
      success: false,
      message: `Exception during manual reset: ${error}`,
      reset_count: 0
    };
  }
}

/**
 * Get reset statistics
 */
export async function getResetStatistics(): Promise<{
  total_players_with_weekly_scores: number;
  total_weekly_score_points: number;
  difficulties: {
    [key: string]: {
      players: number;
      total_score: number;
      avg_score: number;
      highest_score: number;
    };
  };
} | null> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('user_stats')
      .select('difficulty, weekly_scores, user_id')
      .gt('weekly_scores', 0);

    if (error) {
      console.error('Error fetching reset statistics:', error);
      return null;
    }

    if (!data || data.length === 0) {
      return {
        total_players_with_weekly_scores: 0,
        total_weekly_score_points: 0,
        difficulties: {}
      };
    }

    const difficulties: { [key: string]: any } = {};
    let totalPlayers = new Set();
    let totalPoints = 0;

    data.forEach(record => {
      const difficulty = record.difficulty;
      const score = record.weekly_scores || 0;
      
      totalPlayers.add(record.user_id);
      totalPoints += score;

      if (!difficulties[difficulty]) {
        difficulties[difficulty] = {
          players: new Set(),
          scores: []
        };
      }

      difficulties[difficulty].players.add(record.user_id);
      difficulties[difficulty].scores.push(score);
    });

    // Process difficulty stats
    Object.keys(difficulties).forEach(difficulty => {
      const diffData = difficulties[difficulty];
      const scores = diffData.scores;
      
      difficulties[difficulty] = {
        players: diffData.players.size,
        total_score: scores.reduce((a: number, b: number) => a + b, 0),
        avg_score: Math.round(scores.reduce((a: number, b: number) => a + b, 0) / scores.length),
        highest_score: Math.max(...scores)
      };
    });

    return {
      total_players_with_weekly_scores: totalPlayers.size,
      total_weekly_score_points: totalPoints,
      difficulties
    };

  } catch (error) {
    console.error('Exception in getResetStatistics:', error);
    return null;
  }
}

/**
 * Preset configurations for common reset schedules
 */
export const PRESET_CONFIGS = {
  MONDAY_MIDNIGHT: {
    reset_day_of_week: 1,
    reset_hour: 0,
    reset_minute: 0,
    description: 'Monday at midnight UTC'
  },
  SUNDAY_MIDNIGHT: {
    reset_day_of_week: 0,
    reset_hour: 0,
    reset_minute: 0,
    description: 'Sunday at midnight UTC'
  },
  FRIDAY_EVENING: {
    reset_day_of_week: 5,
    reset_hour: 18,
    reset_minute: 0,
    description: 'Friday at 6:00 PM UTC'
  },
  SATURDAY_MORNING: {
    reset_day_of_week: 6,
    reset_hour: 9,
    reset_minute: 0,
    description: 'Saturday at 9:00 AM UTC'
  }
};
