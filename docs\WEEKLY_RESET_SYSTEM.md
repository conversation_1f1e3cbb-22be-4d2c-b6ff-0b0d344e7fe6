# Weekly Leaderboard Reset System

This document describes the implementation and usage of the weekly leaderboard reset system for Word Nook.

## Overview

The weekly reset system automatically resets player weekly scores every week at a configurable time, creating fresh weekly competitions while preserving all-time statistics.

## Features

- **Automatic Weekly Resets**: Configurable day and time for automatic resets
- **Weekly Leaderboards**: Separate leaderboards for weekly and all-time rankings
- **Reset Countdown**: Visual countdown showing time until next reset
- **Manual Reset**: Admin functionality for testing and manual resets
- **Reset Notifications**: User notifications when resets occur
- **Statistics Preservation**: Previous weekly scores are preserved before reset

## Database Schema

### New Tables

#### `system_settings`
Stores system-wide configuration including weekly reset settings.

```sql
CREATE TABLE system_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  setting_key TEXT UNIQUE NOT NULL,
  setting_value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Modified Tables

#### `user_stats` (New Columns)
- `last_weekly_reset`: Timestamp of user's last weekly reset
- `weekly_rank`: Current weekly ranking position
- `previous_weekly_score`: Score from previous week (preserved during reset)

## Database Functions

### `get_next_weekly_reset()`
Returns the timestamp of the next scheduled weekly reset.

### `is_weekly_reset_due()`
Checks if a weekly reset is currently due based on configuration.

### `perform_weekly_reset()`
Performs the actual weekly reset, preserving previous scores and resetting current ones.

## Configuration

### Reset Schedule
Configure when weekly resets occur:

```typescript
interface WeeklyResetConfig {
  reset_day_of_week: number; // 0=Sunday, 1=Monday, etc.
  reset_hour: number; // 0-23 (UTC)
  reset_minute: number; // 0-59
  is_enabled: boolean;
}
```

### Preset Configurations
- **Monday Midnight**: Default weekly reset
- **Sunday Midnight**: Traditional week start
- **Friday Evening**: End-of-workweek reset
- **Saturday Morning**: Weekend competition start

## Components

### `WeeklyResetProvider`
Context provider that manages automatic reset checking across the application.

```tsx
<WeeklyResetProvider>
  <App />
</WeeklyResetProvider>
```

### `WeeklyResetCountdown`
Displays countdown to next reset with optional force reset button.

```tsx
<WeeklyResetCountdown 
  showForceReset={true} 
  onForceReset={handleManualReset} 
/>
```

### `WeeklyLeaderboard`
Shows weekly rankings with tabs for different difficulties.

```tsx
<WeeklyLeaderboard className="w-full" />
```

### `WeeklyResetNotification`
Shows notifications when resets occur.

```tsx
<WeeklyResetNotification className="mb-4" />
```

## Hooks

### `useWeeklyReset()`
Main hook for managing weekly reset functionality:

```typescript
const {
  resetInfo,
  isLoading,
  isResetInProgress,
  lastResetResult,
  checkReset,
  forceReset,
  refreshResetInfo
} = useWeeklyReset();
```

### `useWeeklyResetInfo()`
Lightweight hook for components that only need reset information:

```typescript
const {
  resetInfo,
  isLoading,
  refreshResetInfo
} = useWeeklyResetInfo();
```

## Utility Functions

### Weekly Leaderboard Utils
- `getWeeklyLeaderboardByDifficulty()`: Get weekly rankings for specific difficulty
- `getGlobalWeeklyLeaderboard()`: Get global weekly rankings
- `updateWeeklyScore()`: Update player's weekly score after match
- `checkAndPerformWeeklyReset()`: Check and perform reset if due

### Configuration Utils
- `getWeeklyResetConfig()`: Get current reset configuration
- `updateWeeklyResetConfig()`: Update reset configuration
- `performManualReset()`: Manually trigger a reset
- `getResetStatistics()`: Get statistics about current weekly scores

## Integration Points

### Match Completion
Weekly scores are automatically updated when matches are completed in `saveMatchToHistory()`:

```typescript
// Update weekly score with the player's match score
const weeklyScoreUpdated = await updateWeeklyScore(
  record.player_id,
  record.difficulty,
  record.score
);
```

### Hall of Fame Page
The hall of fame page now includes tabs for both all-time and weekly leaderboards:

```tsx
<Tabs defaultValue="all-time">
  <TabsTrigger value="all-time">All-Time</TabsTrigger>
  <TabsTrigger value="weekly">Weekly</TabsTrigger>
  
  <TabsContent value="all-time">
    {/* Existing all-time leaderboards */}
  </TabsContent>
  
  <TabsContent value="weekly">
    <WeeklyResetCountdown />
    <WeeklyLeaderboard />
  </TabsContent>
</Tabs>
```

## Automatic Reset Logic

The system automatically checks for and performs resets:

1. **On App Load**: Initial check when user visits the application
2. **Periodic Checks**: Every 30 minutes while app is active
3. **Focus Events**: When user returns to the application
4. **Visibility Changes**: When browser tab becomes active

## Error Handling

- **Database Errors**: Graceful handling of database connection issues
- **Configuration Errors**: Validation of reset configuration parameters
- **Reset Failures**: Comprehensive error reporting and recovery
- **Network Issues**: Retry logic for failed reset attempts

## Testing

### Manual Reset
For testing purposes, use the force reset functionality:

```typescript
const result = await performManualReset(true);
console.log(result.message);
```

### Configuration Testing
Test different reset schedules:

```typescript
await updateWeeklyResetConfig({
  reset_day_of_week: 1, // Monday
  reset_hour: 0,        // Midnight
  reset_minute: 0       // :00
});
```

## Security Considerations

- Reset configuration changes should be restricted to admin users
- Manual reset functionality should be protected
- Database functions include proper validation
- RLS policies control access to system settings

## Performance Considerations

- Weekly leaderboard queries are optimized with indexes
- Reset operations are batched for efficiency
- Automatic checks are throttled to prevent excessive database calls
- Previous scores are preserved efficiently using JSONB updates

## Monitoring

Monitor the weekly reset system through:

- Reset success/failure logs
- Weekly score statistics
- Player participation metrics
- Reset timing accuracy

## Future Enhancements

Potential improvements to consider:

- **Multiple Reset Periods**: Monthly, daily, or custom periods
- **Seasonal Competitions**: Special event-based resets
- **Reset Notifications**: Email/push notifications for resets
- **Advanced Statistics**: Trend analysis and historical comparisons
- **Admin Dashboard**: Comprehensive management interface
