import { createClient } from "../../supabase/client";

export interface LeaderboardEntry {
  player_id: string;
  display_name: string;
  avatar_url?: string;
  avatar_border?: string;
  composite_score: number;
  final_score: number;
  survival_rate: number;
  accuracy_rate: number;
  speed_bonus: number;
  matches_played: number;
  highest_round: number;
  total_eliminations: number;
  avg_elimination_round: number;
  last_played: string;
  difficulty: string;
}

export interface LeaderboardStats {
  total_players: number;
  total_matches: number;
  avg_score: number;
  highest_score: number;
}

// Difficulty multipliers for composite scoring
const DIFFICULTY_MULTIPLIERS = {
  easy: 1.0,
  medium: 1.2,
  hard: 1.5,
  extreme: 2.0
};

// Composite scoring weights (from memories)
const SCORING_WEIGHTS = {
  final_score: 0.40,    // 40%
  survival_rate: 0.25,  // 25%
  accuracy_rate: 0.20,  // 20%
  speed_bonus: 0.15     // 15%
};

/**
 * Calculate composite score based on leaderboard ranking system
 */
export function calculateCompositeScore(
  finalScore: number,
  survivalRate: number,
  accuracyRate: number,
  speedBonus: number,
  difficulty: string
): number {
  const baseScore = (
    finalScore * SCORING_WEIGHTS.final_score +
    survivalRate * SCORING_WEIGHTS.survival_rate +
    accuracyRate * SCORING_WEIGHTS.accuracy_rate +
    speedBonus * SCORING_WEIGHTS.speed_bonus
  );

  const multiplier = DIFFICULTY_MULTIPLIERS[difficulty.toLowerCase() as keyof typeof DIFFICULTY_MULTIPLIERS] || 1.0;
  return Math.round(baseScore * multiplier);
}

/**
 * Get leaderboard data for a specific difficulty
 */
export async function getLeaderboardByDifficulty(difficulty: string, limit: number = 10): Promise<LeaderboardEntry[]> {
  const supabase = createClient();

  try {
    // Get match history data with player information
    const { data: matchData, error } = await supabase
      .from('match_history_players')
      .select(`
        player_id,
        score,
        final_rank,
        elimination_round,
        difficulty,
        created_at,
        match_id,
        players (
          id,
          display_name,
          avatar_url,
          avatar_border
        ),
        match_histories (
          current_round,
          match_duration_seconds,
          ended_at
        )
      `)
      .eq('difficulty', difficulty)
      .order('score', { ascending: false });

    if (error) {
      console.error('Error fetching leaderboard data:', error);
      return [];
    }

    if (!matchData || matchData.length === 0) {
      return [];
    }

    // Group by player and calculate aggregate stats
    const playerStats = new Map<string, any>();

    matchData.forEach((match: any) => {
      const playerId = match.player_id;
      
      if (!playerStats.has(playerId)) {
        playerStats.set(playerId, {
          player_id: playerId,
          display_name: match.players?.display_name || 'Unknown Player',
          avatar_url: match.players?.avatar_url,
          avatar_border: match.players?.avatar_border,
          difficulty: difficulty,
          scores: [],
          eliminations: [],
          total_rounds: [],
          match_durations: [],
          ranks: [],
          last_played: match.created_at
        });
      }

      const stats = playerStats.get(playerId);
      stats.scores.push(match.score || 0);
      stats.eliminations.push(match.elimination_round || 0);
      stats.total_rounds.push(match.match_histories?.current_round || 1);
      stats.match_durations.push(match.match_histories?.match_duration_seconds || 0);
      stats.ranks.push(match.final_rank || 999);
      
      // Keep track of most recent match
      if (new Date(match.created_at) > new Date(stats.last_played)) {
        stats.last_played = match.created_at;
      }
    });

    // Calculate composite scores for each player
    const leaderboardEntries: LeaderboardEntry[] = [];

    playerStats.forEach((stats) => {
      const matches_played = stats.scores.length;
      const final_score = Math.max(...stats.scores); // Best score
      const avg_score = stats.scores.reduce((a: number, b: number) => a + b, 0) / matches_played;
      
      // Calculate survival rate (percentage of matches where player wasn't eliminated early)
      const survived_matches = stats.eliminations.filter((round: number) => round === 0 || round > 5).length;
      const survival_rate = (survived_matches / matches_played) * 100;
      
      // Calculate accuracy rate (based on performance vs average)
      const accuracy_rate = Math.min(100, (avg_score / 500) * 100); // Assuming 500 is a good baseline
      
      // Calculate speed bonus (based on average elimination round - higher is better)
      const avg_elimination_round = stats.eliminations.reduce((a: number, b: number) => a + b, 0) / matches_played;
      const speed_bonus = Math.min(100, avg_elimination_round * 10); // Scale to 0-100
      
      // Calculate composite score
      const composite_score = calculateCompositeScore(
        final_score,
        survival_rate,
        accuracy_rate,
        speed_bonus,
        difficulty
      );

      leaderboardEntries.push({
        player_id: stats.player_id,
        display_name: stats.display_name,
        avatar_url: stats.avatar_url,
        avatar_border: stats.avatar_border,
        composite_score,
        final_score,
        survival_rate: Math.round(survival_rate * 100) / 100,
        accuracy_rate: Math.round(accuracy_rate * 100) / 100,
        speed_bonus: Math.round(speed_bonus * 100) / 100,
        matches_played,
        highest_round: Math.max(...stats.total_rounds),
        total_eliminations: stats.eliminations.filter((r: number) => r > 0).length,
        avg_elimination_round: Math.round(avg_elimination_round * 100) / 100,
        last_played: stats.last_played,
        difficulty: difficulty
      });
    });

    // Sort by composite score and return top entries
    return leaderboardEntries
      .sort((a, b) => b.composite_score - a.composite_score)
      .slice(0, limit);

  } catch (error) {
    console.error('Exception in getLeaderboardByDifficulty:', error);
    return [];
  }
}

/**
 * Get overall leaderboard stats for a difficulty
 */
export async function getLeaderboardStats(difficulty: string): Promise<LeaderboardStats> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('match_history_players')
      .select('score, player_id')
      .eq('difficulty', difficulty);

    if (error || !data) {
      return {
        total_players: 0,
        total_matches: 0,
        avg_score: 0,
        highest_score: 0
      };
    }

    const uniquePlayers = new Set(data.map(d => d.player_id));
    const scores = data.map(d => d.score || 0);

    return {
      total_players: uniquePlayers.size,
      total_matches: data.length,
      avg_score: Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) || 0,
      highest_score: Math.max(...scores) || 0
    };

  } catch (error) {
    console.error('Exception in getLeaderboardStats:', error);
    return {
      total_players: 0,
      total_matches: 0,
      avg_score: 0,
      highest_score: 0
    };
  }
}

/**
 * Get global leaderboard across all difficulties
 */
export async function getGlobalLeaderboard(limit: number = 20): Promise<LeaderboardEntry[]> {
  const difficulties = ['easy', 'medium', 'hard', 'extreme'];
  const allEntries: LeaderboardEntry[] = [];

  for (const difficulty of difficulties) {
    const entries = await getLeaderboardByDifficulty(difficulty, limit);
    allEntries.push(...entries);
  }

  // Sort by composite score and return top entries
  return allEntries
    .sort((a, b) => b.composite_score - a.composite_score)
    .slice(0, limit);
}
