import { useState, useEffect, useCallback } from 'react';
import { 
  getWeeklyResetInfo, 
  checkAndPerformWeeklyReset, 
  WeeklyResetInfo, 
  WeeklyResetResult 
} from '@/utils/weekly-leaderboard-utils';

interface UseWeeklyResetReturn {
  resetInfo: WeeklyResetInfo | null;
  isLoading: boolean;
  isResetInProgress: boolean;
  lastResetResult: WeeklyResetResult | null;
  checkReset: () => Promise<void>;
  forceReset: () => Promise<WeeklyResetResult>;
  refreshResetInfo: () => Promise<void>;
}

/**
 * Custom hook for managing weekly reset functionality
 * Automatically checks for and performs weekly resets when needed
 */
export function useWeeklyReset(): UseWeeklyResetReturn {
  const [resetInfo, setResetInfo] = useState<WeeklyResetInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isResetInProgress, setIsResetInProgress] = useState(false);
  const [lastResetResult, setLastResetResult] = useState<WeeklyResetResult | null>(null);

  /**
   * Fetch current weekly reset information
   */
  const refreshResetInfo = useCallback(async () => {
    try {
      setIsLoading(true);
      const info = await getWeeklyResetInfo();
      setResetInfo(info);
    } catch (error) {
      console.error('Error refreshing reset info:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Check if reset is due and perform it automatically
   */
  const checkReset = useCallback(async () => {
    if (isResetInProgress) return;

    try {
      setIsResetInProgress(true);
      
      // Get current reset info
      const info = await getWeeklyResetInfo();
      if (!info) return;

      // If reset is due, perform it automatically
      if (info.is_reset_due) {
        console.log('Weekly reset is due, performing automatic reset...');
        const result = await checkAndPerformWeeklyReset();
        setLastResetResult(result);

        if (result.success) {
          console.log(`Weekly reset completed: ${result.message}`);
          // Refresh reset info after successful reset
          await refreshResetInfo();
        } else {
          console.error(`Weekly reset failed: ${result.message}`);
        }
      }
    } catch (error) {
      console.error('Error during automatic reset check:', error);
      setLastResetResult({
        success: false,
        message: `Error during reset check: ${error}`,
        reset_count: 0
      });
    } finally {
      setIsResetInProgress(false);
    }
  }, [isResetInProgress, refreshResetInfo]);

  /**
   * Force a weekly reset (for testing or manual reset)
   */
  const forceReset = useCallback(async (): Promise<WeeklyResetResult> => {
    if (isResetInProgress) {
      return {
        success: false,
        message: 'Reset already in progress',
        reset_count: 0
      };
    }

    try {
      setIsResetInProgress(true);
      const result = await checkAndPerformWeeklyReset();
      setLastResetResult(result);

      if (result.success) {
        // Refresh reset info after successful reset
        await refreshResetInfo();
      }

      return result;
    } catch (error) {
      const errorResult = {
        success: false,
        message: `Error during forced reset: ${error}`,
        reset_count: 0
      };
      setLastResetResult(errorResult);
      return errorResult;
    } finally {
      setIsResetInProgress(false);
    }
  }, [isResetInProgress, refreshResetInfo]);

  // Initialize and set up automatic checking
  useEffect(() => {
    let mounted = true;
    let checkInterval: NodeJS.Timeout;

    const initialize = async () => {
      if (!mounted) return;

      // Initial load of reset info
      await refreshResetInfo();

      if (!mounted) return;

      // Perform initial reset check
      await checkReset();

      if (!mounted) return;

      // Set up periodic checking every 30 minutes
      checkInterval = setInterval(async () => {
        if (mounted) {
          await checkReset();
        }
      }, 30 * 60 * 1000); // 30 minutes
    };

    initialize();

    return () => {
      mounted = false;
      if (checkInterval) {
        clearInterval(checkInterval);
      }
    };
  }, [checkReset, refreshResetInfo]);

  // Additional effect to check reset when user becomes active
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && resetInfo) {
        // User became active, check if reset is needed
        checkReset();
      }
    };

    const handleFocus = () => {
      if (resetInfo) {
        checkReset();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [checkReset, resetInfo]);

  return {
    resetInfo,
    isLoading,
    isResetInProgress,
    lastResetResult,
    checkReset,
    forceReset,
    refreshResetInfo
  };
}

/**
 * Hook for components that only need reset info without automatic checking
 */
export function useWeeklyResetInfo() {
  const [resetInfo, setResetInfo] = useState<WeeklyResetInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const refreshResetInfo = useCallback(async () => {
    try {
      setIsLoading(true);
      const info = await getWeeklyResetInfo();
      setResetInfo(info);
    } catch (error) {
      console.error('Error fetching reset info:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    refreshResetInfo();
  }, [refreshResetInfo]);

  return {
    resetInfo,
    isLoading,
    refreshResetInfo
  };
}
