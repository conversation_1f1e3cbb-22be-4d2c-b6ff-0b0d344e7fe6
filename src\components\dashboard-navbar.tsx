"use client";

import Link from "next/link";
import { Sheet, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger, SheetClose } from "./ui/sheet";
import { Button } from "./ui/button";
import { Menu } from "lucide-react";
import { useState } from "react";
import UserProfile from "./user-profile";
import Image from "next/image";

export default function DashboardNavbar() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <nav className="w-full border-b border-stone-300 bg-[#fff8e8] py-2">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <div className="flex items-center">
          <div>
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6 text-[#8B4513]" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="bg-[#fff8e8] w-64">
                <div className="flex flex-col gap-4 mt-8">
                  <SheetClose asChild>
                    <Link href="/">
                      <Button className="w-full bg-[#8B4513] hover:bg-[#A0522D]">
                        Home
                      </Button>
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link href="/dashboard">
                      <Button className="w-full bg-[#8B4513] hover:bg-[#A0522D]">
                        Dashboard
                      </Button>
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link href="/hall-of-fame">
                      <Button className="w-full bg-[#8B4513] hover:bg-[#A0522D]">
                        Leaderboard
                      </Button>
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link href="/levels">
                      <Button className="w-full bg-[#8B4513] hover:bg-[#A0522D]">
                        Levels
                      </Button>
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link href="/profile">
                      <Button className="w-full bg-[#8B4513] hover:bg-[#A0522D]">
                        Profile
                      </Button>
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link href="/feedback">
                      <Button className="w-full bg-[#8B4513] hover:bg-[#A0522D]">
                        Feedback
                      </Button>
                    </Link>
                  </SheetClose>
                  <SheetClose asChild>
                    <Link href="/settings">
                      <Button className="w-full bg-[#8B4513] hover:bg-[#A0522D]">
                        Settings
                      </Button>
                    </Link>
                  </SheetClose>
                </div>
              </SheetContent>
            </Sheet>
          </div>
          <Link href="/" prefetch className="flex items-center">
            <Image
              src="/logo2.png"
              alt="Word Nook Logo"
              width={120}
              height={40}
            />
          </Link>
        </div>

        <UserProfile />
      </div>
    </nav>
  );
}
