'use client';

import React, { useState, useEffect } from 'react';
import { Clock, Calendar, RotateCcw } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useWeeklyResetInfo } from '@/hooks/useWeeklyReset';
import { WeeklyResetInfo } from '@/utils/weekly-leaderboard-utils';
import { performManualReset } from '@/utils/weekly-reset-config';

interface WeeklyResetCountdownProps {
  className?: string;
  showForceReset?: boolean;
  onForceReset?: () => Promise<void>;
}

/**
 * Component that displays countdown to next weekly reset
 */
export function WeeklyResetCountdown({ 
  className, 
  showForceReset = false, 
  onForceReset 
}: WeeklyResetCountdownProps) {
  const { resetInfo, isLoading, refreshResetInfo } = useWeeklyResetInfo();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  // Calculate real-time countdown
  const getRealTimeCountdown = (resetInfo: WeeklyResetInfo) => {
    const now = currentTime.getTime();
    const resetTime = new Date(resetInfo.next_reset_date).getTime();
    const timeDiff = resetTime - now;

    if (timeDiff <= 0) {
      return {
        days: 0,
        hours: 0,
        minutes: 0,
        isOverdue: true
      };
    }

    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    return {
      days,
      hours,
      minutes,
      isOverdue: false
    };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });
  };

  const handleForceReset = async () => {
    if (onForceReset) {
      await onForceReset();
    } else {
      // Default force reset implementation
      try {
        const result = await performManualReset(true);
        if (result.success) {
          console.log(`Force reset completed: ${result.message}`);
        } else {
          console.error(`Force reset failed: ${result.message}`);
        }
      } catch (error) {
        console.error('Error during force reset:', error);
      }
    }
    await refreshResetInfo();
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Weekly Reset
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-amber-600"></div>
            <span className="ml-2 text-gray-600">Loading reset info...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!resetInfo) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Weekly Reset
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-gray-500">
            <Calendar className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p>Reset information unavailable</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const countdown = getRealTimeCountdown(resetInfo);

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-amber-600" />
            Weekly Reset
          </CardTitle>
          {resetInfo.is_reset_due && (
            <Badge variant="destructive" className="animate-pulse">
              Reset Due
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Countdown Display */}
        <div className="text-center">
          {countdown.isOverdue ? (
            <div className="space-y-2">
              <Badge variant="destructive" className="text-lg px-4 py-2">
                Reset Overdue
              </Badge>
              <p className="text-sm text-gray-600">
                Weekly reset should have occurred
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex justify-center gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-amber-600">
                    {countdown.days}
                  </div>
                  <div className="text-xs text-gray-500">Days</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-amber-600">
                    {countdown.hours}
                  </div>
                  <div className="text-xs text-gray-500">Hours</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-amber-600">
                    {countdown.minutes}
                  </div>
                  <div className="text-xs text-gray-500">Minutes</div>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Until next weekly reset
              </p>
            </div>
          )}
        </div>

        {/* Reset Information */}
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Next Reset:</span>
            <span className="font-medium">
              {formatDate(resetInfo.next_reset_date)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Last Reset:</span>
            <span className="font-medium">
              {formatDate(resetInfo.last_reset_date)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Current Week:</span>
            <span className="font-medium">
              Week {resetInfo.current_week} of {resetInfo.current_year}
            </span>
          </div>
        </div>

        {/* Force Reset Button (for testing/admin) */}
        {showForceReset && (
          <div className="pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={handleForceReset}
              className="w-full"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Force Reset (Testing)
            </Button>
          </div>
        )}

        {/* Reset Schedule Info */}
        <div className="text-xs text-gray-500 text-center pt-2 border-t">
          Weekly leaderboards reset every Monday at midnight UTC
        </div>
      </CardContent>
    </Card>
  );
}
