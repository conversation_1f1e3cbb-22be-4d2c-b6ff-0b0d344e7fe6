'use client';

import React, { useState, useEffect } from 'react';
import { Trophy, TrendingUp, TrendingDown, Minus, Calendar, Users } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import StyledAvatar from '@/components/styled-avatar';
import PlayerStatsPopup from '@/components/player-stats-popup';
import { 
  getWeeklyLeaderboardByDifficulty, 
  getGlobalWeeklyLeaderboard,
  getWeeklyLeaderboardStats,
  WeeklyLeaderboardEntry 
} from '@/utils/weekly-leaderboard-utils';

interface WeeklyLeaderboardProps {
  className?: string;
}

interface WeeklyStats {
  total_players: number;
  total_weekly_score: number;
  avg_weekly_score: number;
  highest_weekly_score: number;
}

/**
 * Component that displays weekly leaderboards
 */
export function WeeklyLeaderboard({ className }: WeeklyLeaderboardProps) {
  const [activeTab, setActiveTab] = useState('global');
  const [leaderboards, setLeaderboards] = useState<{
    global: WeeklyLeaderboardEntry[];
    easy: WeeklyLeaderboardEntry[];
    medium: WeeklyLeaderboardEntry[];
    hard: WeeklyLeaderboardEntry[];
    extreme: WeeklyLeaderboardEntry[];
  }>({
    global: [],
    easy: [],
    medium: [],
    hard: [],
    extreme: []
  });
  const [stats, setStats] = useState<{
    easy: WeeklyStats;
    medium: WeeklyStats;
    hard: WeeklyStats;
    extreme: WeeklyStats;
  }>({
    easy: { total_players: 0, total_weekly_score: 0, avg_weekly_score: 0, highest_weekly_score: 0 },
    medium: { total_players: 0, total_weekly_score: 0, avg_weekly_score: 0, highest_weekly_score: 0 },
    hard: { total_players: 0, total_weekly_score: 0, avg_weekly_score: 0, highest_weekly_score: 0 },
    extreme: { total_players: 0, total_weekly_score: 0, avg_weekly_score: 0, highest_weekly_score: 0 }
  });
  const [isLoading, setIsLoading] = useState(true);

  // Load weekly leaderboard data
  useEffect(() => {
    const loadWeeklyLeaderboards = async () => {
      setIsLoading(true);
      try {
        // Load leaderboards for each difficulty
        const [globalData, easyData, mediumData, hardData, extremeData] = await Promise.all([
          getGlobalWeeklyLeaderboard(15),
          getWeeklyLeaderboardByDifficulty('easy', 10),
          getWeeklyLeaderboardByDifficulty('medium', 10),
          getWeeklyLeaderboardByDifficulty('hard', 10),
          getWeeklyLeaderboardByDifficulty('extreme', 10)
        ]);

        // Load stats for each difficulty
        const [easyStats, mediumStats, hardStats, extremeStats] = await Promise.all([
          getWeeklyLeaderboardStats('easy'),
          getWeeklyLeaderboardStats('medium'),
          getWeeklyLeaderboardStats('hard'),
          getWeeklyLeaderboardStats('extreme')
        ]);

        setLeaderboards({
          global: globalData,
          easy: easyData,
          medium: mediumData,
          hard: hardData,
          extreme: extremeData
        });

        setStats({
          easy: easyStats,
          medium: mediumStats,
          hard: hardStats,
          extreme: extremeStats
        });
      } catch (error) {
        console.error('Error loading weekly leaderboards:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadWeeklyLeaderboards();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const getRankStyling = (index: number) => {
    if (index === 0) return "bg-gradient-to-r from-yellow-400 to-yellow-500 text-white shadow-lg";
    if (index === 1) return "bg-gradient-to-r from-gray-300 to-gray-400 text-white shadow-md";
    if (index === 2) return "bg-gradient-to-r from-amber-600 to-amber-700 text-white shadow-md";
    return "bg-gradient-to-r from-amber-100 to-amber-200 text-amber-800";
  };

  const getScoreChangeIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  const difficultyConfigs = {
    easy: { name: 'Easy', color: 'text-green-500', bgColor: 'bg-green-100', borderColor: 'border-green-200' },
    medium: { name: 'Medium', color: 'text-blue-500', bgColor: 'bg-blue-100', borderColor: 'border-blue-200' },
    hard: { name: 'Hard', color: 'text-orange-500', bgColor: 'bg-orange-100', borderColor: 'border-orange-200' },
    extreme: { name: 'Extreme', color: 'text-purple-500', bgColor: 'bg-purple-100', borderColor: 'border-purple-200' }
  };

  const renderLeaderboardContent = (entries: WeeklyLeaderboardEntry[], difficulty?: string) => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-amber-600"></div>
          <span className="ml-2 text-gray-600">Loading weekly leaderboard...</span>
        </div>
      );
    }

    if (entries.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <Trophy className="h-12 w-12 mx-auto mb-3 text-gray-300" />
          <p className="text-lg font-medium">No weekly scores yet</p>
          <p className="text-sm">Play some matches to appear on the weekly leaderboard!</p>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {entries.map((player, index) => (
          <div
            key={`${difficulty || 'global'}-${player.player_id}`}
            className="flex items-center justify-between p-3 hover:bg-gray-50/50 transition-colors rounded-lg"
          >
            <div className="flex items-center gap-3">
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                getRankStyling(index)
              )}>
                {index + 1}
              </div>
              <PlayerStatsPopup
                playerId={player.player_id}
                playerName={player.display_name}
                playerAvatar={player.avatar_url}
                playerBorder={player.avatar_border}
              >
                <div className="flex items-center gap-3 cursor-pointer hover:bg-gray-50 rounded p-1 transition-colors">
                  <StyledAvatar
                    src={player.avatar_url}
                    alt={player.display_name}
                    fallback={player.display_name.substring(0, 2).toUpperCase()}
                    size="sm"
                    border={player.avatar_border}
                  />
                  <div className="flex-1 min-w-0">
                    <p className="font-semibold text-gray-900 truncate">{player.display_name}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs capitalize">
                        {player.difficulty}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {player.matches_played_this_week} matches
                      </span>
                    </div>
                  </div>
                </div>
              </PlayerStatsPopup>
            </div>

            <div className="text-right">
              <div className="flex items-center gap-2">
                <span className="font-bold text-lg text-amber-700">
                  {player.weekly_score.toLocaleString()}
                </span>
                {getScoreChangeIcon(player.weekly_score, player.previous_weekly_score)}
              </div>
              {player.previous_weekly_score > 0 && (
                <p className="text-xs text-gray-500">
                  Last week: {player.previous_weekly_score.toLocaleString()}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-amber-600" />
            Weekly Leaderboard
          </CardTitle>
          <Badge variant="secondary" className="bg-amber-100 text-amber-800">
            This Week
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="global">Global</TabsTrigger>
            <TabsTrigger value="easy">Easy</TabsTrigger>
            <TabsTrigger value="medium">Medium</TabsTrigger>
            <TabsTrigger value="hard">Hard</TabsTrigger>
            <TabsTrigger value="extreme">Extreme</TabsTrigger>
          </TabsList>

          <TabsContent value="global" className="mt-4">
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-semibold">Global Weekly Champions</h3>
                <p className="text-sm text-gray-600">Top performers across all difficulties</p>
              </div>
              {renderLeaderboardContent(leaderboards.global)}
            </div>
          </TabsContent>

          {Object.entries(difficultyConfigs).map(([key, config]) => (
            <TabsContent key={key} value={key} className="mt-4">
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-lg font-semibold">{config.name} Weekly Leaderboard</h3>
                  <div className="flex justify-center gap-4 mt-2 text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {stats[key as keyof typeof stats].total_players} players
                    </span>
                    <span>
                      Highest: {stats[key as keyof typeof stats].highest_weekly_score.toLocaleString()}
                    </span>
                  </div>
                </div>
                {renderLeaderboardContent(leaderboards[key as keyof typeof leaderboards], key)}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
}
