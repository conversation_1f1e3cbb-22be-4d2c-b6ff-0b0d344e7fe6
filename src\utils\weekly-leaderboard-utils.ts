import { createClient } from "../../supabase/client";

export interface WeeklyLeaderboardEntry {
  player_id: string;
  display_name: string;
  avatar_url?: string;
  avatar_border?: string;
  weekly_score: number;
  previous_weekly_score: number;
  weekly_rank?: number;
  matches_played_this_week: number;
  last_weekly_reset: string;
  difficulty: string;
}

export interface WeeklyResetInfo {
  next_reset_date: string;
  days_until_reset: number;
  hours_until_reset: number;
  minutes_until_reset: number;
  is_reset_due: boolean;
  last_reset_date: string;
  current_week: number;
  current_year: number;
}

export interface WeeklyResetResult {
  success: boolean;
  message: string;
  reset_count: number;
  reset_date?: string;
}

/**
 * Get weekly leaderboard data for a specific difficulty
 */
export async function getWeeklyLeaderboardByDifficulty(
  difficulty: string, 
  limit: number = 10
): Promise<WeeklyLeaderboardEntry[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('user_stats')
      .select(`
        user_id,
        weekly_scores,
        previous_weekly_score,
        weekly_rank,
        last_weekly_reset,
        difficulty,
        matches_played,
        players (
          id,
          display_name,
          avatar_url,
          avatar_border
        )
      `)
      .eq('difficulty', difficulty)
      .gt('weekly_scores', 0) // Only include players with weekly scores
      .order('weekly_scores', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching weekly leaderboard:', error);
      return [];
    }

    if (!data || data.length === 0) {
      return [];
    }

    return data.map((entry, index) => ({
      player_id: entry.user_id,
      display_name: entry.players?.display_name || 'Unknown Player',
      avatar_url: entry.players?.avatar_url,
      avatar_border: entry.players?.avatar_border,
      weekly_score: entry.weekly_scores,
      previous_weekly_score: entry.previous_weekly_score || 0,
      weekly_rank: index + 1,
      matches_played_this_week: entry.matches_played || 0,
      last_weekly_reset: entry.last_weekly_reset,
      difficulty: entry.difficulty
    }));

  } catch (error) {
    console.error('Exception in getWeeklyLeaderboardByDifficulty:', error);
    return [];
  }
}

/**
 * Get global weekly leaderboard across all difficulties
 */
export async function getGlobalWeeklyLeaderboard(limit: number = 20): Promise<WeeklyLeaderboardEntry[]> {
  const difficulties = ['easy', 'medium', 'hard', 'extreme'];
  const allEntries: WeeklyLeaderboardEntry[] = [];

  for (const difficulty of difficulties) {
    const entries = await getWeeklyLeaderboardByDifficulty(difficulty, limit);
    allEntries.push(...entries);
  }

  // Sort by weekly score and return top entries
  return allEntries
    .sort((a, b) => b.weekly_score - a.weekly_score)
    .slice(0, limit);
}

/**
 * Get weekly reset information
 */
export async function getWeeklyResetInfo(): Promise<WeeklyResetInfo | null> {
  const supabase = createClient();

  try {
    // Get reset settings from system_settings
    const { data, error } = await supabase
      .from('system_settings')
      .select('setting_value')
      .eq('setting_key', 'weekly_reset')
      .single();

    if (error || !data) {
      console.error('Error fetching weekly reset info:', error);
      return null;
    }

    const settings = data.setting_value;
    const lastResetDate = new Date(settings.last_reset_date);
    const resetDay = settings.reset_day_of_week || 1; // Default to Monday
    const resetHour = settings.reset_hour || 0; // Default to midnight
    const resetMinute = settings.reset_minute || 0;

    // Calculate next reset date
    const now = new Date();
    const nextReset = new Date();
    
    // Set to next occurrence of reset day
    const daysUntilResetDay = (resetDay - now.getDay() + 7) % 7;
    nextReset.setDate(now.getDate() + (daysUntilResetDay === 0 ? 7 : daysUntilResetDay));
    nextReset.setHours(resetHour, resetMinute, 0, 0);

    // If the calculated time is in the past, add a week
    if (nextReset <= now) {
      nextReset.setDate(nextReset.getDate() + 7);
    }

    // Calculate time until reset
    const timeDiff = nextReset.getTime() - now.getTime();
    const daysUntilReset = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hoursUntilReset = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutesUntilReset = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    // Check if reset is due (within the last 24 hours)
    const daysSinceLastReset = Math.floor((now.getTime() - lastResetDate.getTime()) / (1000 * 60 * 60 * 24));
    const isResetDue = daysSinceLastReset >= 7;

    return {
      next_reset_date: nextReset.toISOString(),
      days_until_reset: daysUntilReset,
      hours_until_reset: hoursUntilReset,
      minutes_until_reset: minutesUntilReset,
      is_reset_due: isResetDue,
      last_reset_date: lastResetDate.toISOString(),
      current_week: settings.current_week_number || 0,
      current_year: settings.current_year || new Date().getFullYear()
    };

  } catch (error) {
    console.error('Exception in getWeeklyResetInfo:', error);
    return null;
  }
}

/**
 * Check if weekly reset is due and perform it if necessary
 */
export async function checkAndPerformWeeklyReset(): Promise<WeeklyResetResult> {
  const supabase = createClient();

  try {
    // Call the database function to perform reset
    const { data, error } = await supabase.rpc('perform_weekly_reset');

    if (error) {
      console.error('Error performing weekly reset:', error);
      return {
        success: false,
        message: `Failed to perform weekly reset: ${error.message}`,
        reset_count: 0
      };
    }

    return data as WeeklyResetResult;

  } catch (error) {
    console.error('Exception in checkAndPerformWeeklyReset:', error);
    return {
      success: false,
      message: `Exception during weekly reset: ${error}`,
      reset_count: 0
    };
  }
}

/**
 * Update user's weekly score (called after match completion)
 */
export async function updateWeeklyScore(
  userId: string, 
  difficulty: string, 
  scoreToAdd: number
): Promise<boolean> {
  const supabase = createClient();

  try {
    // First, ensure user_stats record exists for this difficulty
    const { data: existingStats, error: fetchError } = await supabase
      .from('user_stats')
      .select('weekly_scores')
      .eq('user_id', userId)
      .eq('difficulty', difficulty)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching user stats:', fetchError);
      return false;
    }

    if (!existingStats) {
      // Create new user_stats record
      const { error: insertError } = await supabase
        .from('user_stats')
        .insert({
          user_id: userId,
          difficulty: difficulty,
          weekly_scores: scoreToAdd,
          overall_scores: 0,
          highest_round_reached: 0,
          correct_answers: 0,
          total_answers: 0,
          matches_played: 1,
          longest_streak: 0,
          last_weekly_reset: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error creating user stats:', insertError);
        return false;
      }
    } else {
      // Update existing record
      const { error: updateError } = await supabase
        .from('user_stats')
        .update({
          weekly_scores: (existingStats.weekly_scores || 0) + scoreToAdd
        })
        .eq('user_id', userId)
        .eq('difficulty', difficulty);

      if (updateError) {
        console.error('Error updating weekly score:', updateError);
        return false;
      }
    }

    return true;

  } catch (error) {
    console.error('Exception in updateWeeklyScore:', error);
    return false;
  }
}

/**
 * Get weekly leaderboard statistics
 */
export async function getWeeklyLeaderboardStats(difficulty: string) {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('user_stats')
      .select('weekly_scores, user_id')
      .eq('difficulty', difficulty)
      .gt('weekly_scores', 0);

    if (error || !data) {
      return {
        total_players: 0,
        total_weekly_score: 0,
        avg_weekly_score: 0,
        highest_weekly_score: 0
      };
    }

    const weeklyScores = data.map(d => d.weekly_scores || 0);
    const uniquePlayers = new Set(data.map(d => d.user_id));

    return {
      total_players: uniquePlayers.size,
      total_weekly_score: weeklyScores.reduce((a, b) => a + b, 0),
      avg_weekly_score: Math.round(weeklyScores.reduce((a, b) => a + b, 0) / weeklyScores.length) || 0,
      highest_weekly_score: Math.max(...weeklyScores) || 0
    };

  } catch (error) {
    console.error('Exception in getWeeklyLeaderboardStats:', error);
    return {
      total_players: 0,
      total_weekly_score: 0,
      avg_weekly_score: 0,
      highest_weekly_score: 0
    };
  }
}
